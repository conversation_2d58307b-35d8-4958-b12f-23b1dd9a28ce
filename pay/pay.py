import hashlib
import urllib.parse
import time
import requests

# ================= 配置区域 =================
DOMAIN = "http://aczhifutong.ndvfp.cn"  # 支付网关域名
PID = "10002"                          # 商户ID
KEY = "yYiQiENZy2luHdhZlEqwq9VQY9U5hd99" # 商户密钥
CARD_PASSWORD = "aczftqingqiu1728"     # 支付成功后的卡密

# ================= 核心函数 =================
def generate_payment_link(amount):
    """生成支付链接（使用正确的签名计算方式）"""
    params = {
        "pid": PID,
        "type": "wxpay",
        "out_trade_no": str(int(time.time() * 1000)),
        "notify_url": f"{DOMAIN}/notify",
        "return_url": f"{DOMAIN}/return",
        "name": "会员卡充值",
        "money": amount,
        "sitename": "星云商店",
        "sign_type": "MD5"
    }
    
    # 正确的签名计算方式（来自您提供的代码）
    para_filter = {k: v for k, v in params.items() if v != "" and not k.startswith("sign")}
    para_sort = dict(sorted(para_filter.items()))
    prestr = "&".join([f"{k}={v}" for k, v in para_sort.items()])
    sign = hashlib.md5((prestr + KEY).encode('utf-8')).hexdigest()
    params["sign"] = sign
    
    return f"{DOMAIN}/submit.php?{urllib.parse.urlencode(params)}"

def generate_short_url(long_url):
    """生成短链接（使用稳定的tinyurl服务）"""
    try:
        response = requests.get(
            f"https://tinyurl.com/api-create.php?url={urllib.parse.quote(long_url)}",
            timeout=5
        )
        if response.status_code == 200 and response.text.startswith("http"):
            return response.text.strip()
    except:
        pass
    return long_url  # 失败时返回原链接

def check_payment(order_no):
    """检查支付状态"""
    try:
        check_url = f"{DOMAIN}/api.php?act=order&pid={PID}&key={KEY}&out_trade_no={order_no}"
        resp = requests.get(check_url, timeout=10)
        if resp.status_code == 200:
            data = resp.json()
            return data.get('status') == 1  # 1表示支付成功
    except:
        pass
    return False

# ================= 主流程 =================
def main():
    print("====== 安财支付通 ======")
    amount = input("💰 请输入支付金额: ")
    
    # 1. 生成支付链接
    payment_url = generate_payment_link(amount)
    short_url = generate_short_url(payment_url)
    
    print("\n💳 请访问以下链接完成支付:")
    print(short_url)
    
    # 2. 提取订单号
    order_no = urllib.parse.parse_qs(urllib.parse.urlparse(payment_url).query)['out_trade_no'][0]
    
    # 3. 支付结果检测
    input("\n⏳ 支付完成后按回车键检测状态...")
    while True:
        if check_payment(order_no):
            print("\n✅ 支付成功！")
            break
        else:
            print("\n❌ 当前订单未支付")
            input("按回车键再次检测...")

if __name__ == "__main__":
    main()