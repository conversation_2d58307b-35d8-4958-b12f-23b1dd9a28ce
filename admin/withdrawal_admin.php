<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

$admin_password = $_GET['admin_password'] ?? '';

// 终极管理员密码
$SUPER_ADMIN_PASSWORD = "yjsyjs_admin_2024";

if (empty($admin_password) || $admin_password !== $SUPER_ADMIN_PASSWORD) {
    die('管理员密码错误或未提供');
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提现管理后台 - 管理员</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .withdrawal-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .withdrawal-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .withdrawal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }
        
        .withdrawal-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .withdrawal-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .qr-thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            cursor: pointer;
            object-fit: cover;
            border: 2px solid #e2e8f0;
        }
        
        .qr-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }
        
        .qr-modal-content {
            background: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 15px;
            width: 90%;
            max-width: 400px;
            text-align: center;
        }
        
        .qr-modal img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
        }
        
        .status-pending {
            background: #fef5e7;
            color: #744210;
        }
        
        .status-approved {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .status-rejected {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .status-completed {
            background: #e6fffa;
            color: #234e52;
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9em;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部信息 -->
        <div class="header">
            <h1><i class="fas fa-crown"></i> 提现管理后台</h1>
            <p>管理员模式 - 处理所有商户提现申请</p>
        </div>

        <!-- 提现列表 -->
        <div class="content">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-list"></i> 提现申请列表</h3>
                    <button class="btn btn-primary" onclick="loadWithdrawals()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                
                <div id="withdrawalList">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 收款码查看模态框 -->
    <div id="qrModal" class="qr-modal">
        <div class="qr-modal-content">
            <span class="close" onclick="closeQrModal()">&times;</span>
            <h3>收款码</h3>
            <img id="qrModalImage" src="" alt="收款码">
        </div>
    </div>

    <!-- 状态更新模态框 -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('statusModal')">&times;</span>
            <h3><i class="fas fa-edit"></i> 更新提现状态</h3>
            
            <form id="statusForm">
                <input type="hidden" id="statusWithdrawalId" name="withdrawal_id">
                
                <div class="form-group">
                    <label for="statusSelect">选择状态</label>
                    <select id="statusSelect" name="status" class="form-control" required>
                        <option value="">请选择状态</option>
                        <option value="pending">待处理</option>
                        <option value="approved">已批准</option>
                        <option value="rejected">已拒绝</option>
                        <option value="completed">已完成</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">更新状态</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('statusModal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 刷新按钮 -->
    <button class="btn btn-primary refresh-btn" onclick="loadWithdrawals()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="script.js"></script>
    <script>
        const ADMIN_PASSWORD = '<?php echo $SUPER_ADMIN_PASSWORD; ?>';
        
        // 页面加载时获取提现列表
        document.addEventListener('DOMContentLoaded', function() {
            loadWithdrawals();
            bindFormEvents();
        });

        // 绑定表单事件
        function bindFormEvents() {
            document.getElementById('statusForm').addEventListener('submit', function(e) {
                e.preventDefault();
                updateWithdrawalStatus();
            });
        }

        // 加载提现列表
        function loadWithdrawals() {
            const container = document.getElementById('withdrawalList');
            showLoading(container);
            
            // 获取所有提现记录（管理员模式）
            fetch('../get_all_withdrawals.php?admin_password=' + ADMIN_PASSWORD)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    displayWithdrawals(data.data);
                } else {
                    container.innerHTML = '<div class="alert alert-error">' + data.message + '</div>';
                }
            })
            .catch(error => {
                container.innerHTML = '<div class="alert alert-error">加载失败: ' + error.message + '</div>';
            });
        }

        // 显示提现列表
        function displayWithdrawals(withdrawals) {
            const container = document.getElementById('withdrawalList');
            
            if (withdrawals.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">暂无提现申请</div>';
                return;
            }
            
            let html = '';
            
            withdrawals.forEach(withdrawal => {
                const statusClass = 'status-' + withdrawal.withdrawal_status;
                const statusText = getStatusText(withdrawal.withdrawal_status);
                
                html += `
                    <div class="withdrawal-card">
                        <div class="withdrawal-header">
                            <div>
                                <h4>提现ID: ${withdrawal.withdrawal_id}</h4>
                                <p><strong>商户:</strong> ${withdrawal.shop_name || withdrawal.merchant_id}</p>
                            </div>
                            <div>
                                <span class="status-badge ${statusClass}">${statusText}</span>
                            </div>
                        </div>
                        
                        <div class="withdrawal-info">
                            <div>
                                <strong>提现金额:</strong><br>
                                ¥${withdrawal.withdrawal_amount}
                            </div>
                            <div>
                                <strong>申请时间:</strong><br>
                                ${formatDate(withdrawal.created_at)}
                            </div>
                            <div>
                                <strong>收款码:</strong><br>
                                ${withdrawal.payment_qr_code ? 
                                    `<img src="../admin/${withdrawal.payment_qr_code}" class="qr-thumbnail" onclick="showQrModal('${withdrawal.payment_qr_code}')" title="点击查看大图">` : 
                                    '<span style="color: #999;">无收款码</span>'
                                }
                            </div>
                        </div>
                        
                        <div class="withdrawal-actions">
                            <button class="btn btn-primary btn-sm" onclick="showStatusModal(${withdrawal.withdrawal_id}, '${withdrawal.withdrawal_status}')">
                                <i class="fas fa-edit"></i> 更新状态
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="showQrModal('${withdrawal.payment_qr_code}')" ${!withdrawal.payment_qr_code ? 'disabled' : ''}>
                                <i class="fas fa-qrcode"></i> 查看收款码
                            </button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'approved': '已批准',
                'rejected': '已拒绝',
                'completed': '已完成'
            };
            return statusMap[status] || status;
        }

        // 显示状态更新模态框
        function showStatusModal(withdrawalId, currentStatus) {
            document.getElementById('statusWithdrawalId').value = withdrawalId;
            document.getElementById('statusSelect').value = currentStatus;
            showModal('statusModal');
        }

        // 更新提现状态
        function updateWithdrawalStatus() {
            const withdrawalId = document.getElementById('statusWithdrawalId').value;
            const status = document.getElementById('statusSelect').value;
            
            if (!status) {
                showAlert('请选择状态', 'error');
                return;
            }
            
            apiRequest('../update_withdrawal_status.php', {
                withdrawal_id: withdrawalId,
                admin_password: ADMIN_PASSWORD,
                status: status
            })
            .then(response => {
                if (response.status === 'success') {
                    showAlert('状态更新成功', 'success');
                    closeModal('statusModal');
                    loadWithdrawals(); // 重新加载列表
                } else {
                    showAlert(response.message, 'error');
                }
            })
            .catch(error => {
                showAlert('更新失败: ' + error.message, 'error');
            });
        }

        // 显示收款码模态框
        function showQrModal(qrCodePath) {
            if (!qrCodePath) {
                showAlert('无收款码', 'warning');
                return;
            }
            
            document.getElementById('qrModalImage').src = '../admin/' + qrCodePath;
            document.getElementById('qrModal').style.display = 'block';
        }

        // 关闭收款码模态框
        function closeQrModal() {
            document.getElementById('qrModal').style.display = 'none';
        }

        // 显示模态框
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal, .qr-modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // 自动刷新（每30秒）
        setInterval(loadWithdrawals, 30000);
    </script>
</body>
</html> 