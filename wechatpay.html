<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信支付 - 联系客服购买</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .title {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .qr-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px dashed #e9ecef;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .wechat-id {
            background: #07c160;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            margin: 20px 0;
            display: inline-block;
            letter-spacing: 1px;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #856404;
        }
        
        .instructions h3 {
            margin-bottom: 15px;
            color: #856404;
            font-size: 18px;
        }
        
        .instructions ol {
            text-align: left;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .order-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #1565c0;
        }
        
        .back-btn {
            background: #6c757d;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        
        .back-btn:hover {
            background: #5a6268;
        }
        
        .wechat-icon {
            color: #07c160;
            font-size: 24px;
            margin-right: 8px;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            
            .title {
                font-size: 24px;
            }
            
            .qr-code {
                width: 160px;
                height: 160px;
            }
            
            .wechat-id {
                font-size: 16px;
                padding: 10px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">
            <span class="wechat-icon">💬</span>
            微信支付
        </div>
        <div class="subtitle">
            请添加客服微信完成购买
        </div>
        
        <div class="order-info" id="orderInfo">
            <strong>订单信息加载中...</strong>
        </div>
        
        <div class="qr-container">
            <img src="assets/wechatkefu.png" alt="微信客服二维码" class="qr-code">
            <div class="wechat-id">kmhsg8</div>
            <p style="color: #666; font-size: 14px;">扫描二维码或搜索微信号添加客服</p>
        </div>
        
        <div class="instructions">
            <h3>📋 购买流程</h3>
            <ol>
                <li>扫描上方二维码或搜索微信号 <strong>kmhsg8</strong> 添加客服</li>
                <li>将您的订单号发送给客服</li>
                <li>按照客服指引完成付款</li>
                <li>客服确认付款后会立即发货</li>
            </ol>
        </div>
        
        <a href="shop.php" class="back-btn">返回商城</a>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }
        
        // 显示订单信息
        document.addEventListener('DOMContentLoaded', function() {
            var orderId = getUrlParameter('order_id');
            var orderInfoDiv = document.getElementById('orderInfo');
            
            if (orderId) {
                orderInfoDiv.innerHTML = '<strong>订单号：' + orderId + '</strong><br><small>请将此订单号发送给客服</small>';
            } else {
                orderInfoDiv.innerHTML = '<strong>请联系客服完成购买</strong>';
            }
        });
    </script>
</body>
</html>
