[2025-08-13 00:35:03] [INFO] 收到支付通知
[2025-08-13 00:35:03] [INFO] GET参数: {"pid":"2222","trade_no":"2025081223315529730","out_trade_no":"ORDER17550126996416","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"01f3920f3a55ad6fdb753035d0d32b78","sign_type":"MD5"}
[2025-08-13 00:35:03] [INFO] POST参数: []
[2025-08-13 00:35:03] [INFO] 原始输入: 
[2025-08-13 00:35:03] [INFO] 解析参数 - 订单号: ORDER17550126996416, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 00:35:03] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550126996416&pid=2222&trade_no=2025081223315529730&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 00:35:03] [INFO] 签名验证 - 期望签名: 01f3920f3a55ad6fdb753035d0d32b78, 实际签名: 01f3920f3a55ad6fdb753035d0d32b78
[2025-08-13 00:35:03] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550126996416&payment_status=paid
[2025-08-13 00:35:03] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 00:35:03] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 02:10:51] [INFO] 收到支付通知
[2025-08-13 02:10:51] [INFO] GET参数: {"pid":"2222","trade_no":"2025081302103684110","out_trade_no":"ORDER17550222267471","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"5626f0b6fbf71540287a76931fcbee01","sign_type":"MD5"}
[2025-08-13 02:10:51] [INFO] POST参数: []
[2025-08-13 02:10:51] [INFO] 原始输入: 
[2025-08-13 02:10:51] [INFO] 解析参数 - 订单号: ORDER17550222267471, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 02:10:51] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550222267471&pid=2222&trade_no=2025081302103684110&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 02:10:51] [INFO] 签名验证 - 期望签名: 5626f0b6fbf71540287a76931fcbee01, 实际签名: 5626f0b6fbf71540287a76931fcbee01
[2025-08-13 02:10:51] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550222267471&payment_status=paid
[2025-08-13 02:10:51] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 02:10:51] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 02:11:55] [INFO] 收到支付通知
[2025-08-13 02:11:55] [INFO] GET参数: {"pid":"2222","trade_no":"2025081302103684110","out_trade_no":"ORDER17550222267471","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"5626f0b6fbf71540287a76931fcbee01","sign_type":"MD5"}
[2025-08-13 02:11:55] [INFO] POST参数: []
[2025-08-13 02:11:55] [INFO] 原始输入: 
[2025-08-13 02:11:55] [INFO] 解析参数 - 订单号: ORDER17550222267471, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 02:11:55] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550222267471&pid=2222&trade_no=2025081302103684110&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 02:11:55] [INFO] 签名验证 - 期望签名: 5626f0b6fbf71540287a76931fcbee01, 实际签名: 5626f0b6fbf71540287a76931fcbee01
[2025-08-13 02:11:55] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550222267471&payment_status=paid
[2025-08-13 02:11:55] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 02:11:55] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 02:14:05] [INFO] 收到支付通知
[2025-08-13 02:14:05] [INFO] GET参数: {"pid":"2222","trade_no":"2025081302103684110","out_trade_no":"ORDER17550222267471","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"5626f0b6fbf71540287a76931fcbee01","sign_type":"MD5"}
[2025-08-13 02:14:05] [INFO] POST参数: []
[2025-08-13 02:14:05] [INFO] 原始输入: 
[2025-08-13 02:14:05] [INFO] 解析参数 - 订单号: ORDER17550222267471, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 02:14:05] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550222267471&pid=2222&trade_no=2025081302103684110&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 02:14:05] [INFO] 签名验证 - 期望签名: 5626f0b6fbf71540287a76931fcbee01, 实际签名: 5626f0b6fbf71540287a76931fcbee01
[2025-08-13 02:14:05] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550222267471&payment_status=paid
[2025-08-13 02:14:05] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 02:14:05] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 02:30:08] [INFO] 收到支付通知
[2025-08-13 02:30:08] [INFO] GET参数: {"pid":"2222","trade_no":"2025081302103684110","out_trade_no":"ORDER17550222267471","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"5626f0b6fbf71540287a76931fcbee01","sign_type":"MD5"}
[2025-08-13 02:30:08] [INFO] POST参数: []
[2025-08-13 02:30:08] [INFO] 原始输入: 
[2025-08-13 02:30:08] [INFO] 解析参数 - 订单号: ORDER17550222267471, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 02:30:08] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550222267471&pid=2222&trade_no=2025081302103684110&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 02:30:08] [INFO] 签名验证 - 期望签名: 5626f0b6fbf71540287a76931fcbee01, 实际签名: 5626f0b6fbf71540287a76931fcbee01
[2025-08-13 02:30:08] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550222267471&payment_status=paid
[2025-08-13 02:30:08] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 02:30:08] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 03:06:12] [INFO] 收到支付通知
[2025-08-13 03:06:12] [INFO] GET参数: {"pid":"2222","trade_no":"2025081302103684110","out_trade_no":"ORDER17550222267471","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"5626f0b6fbf71540287a76931fcbee01","sign_type":"MD5"}
[2025-08-13 03:06:12] [INFO] POST参数: []
[2025-08-13 03:06:12] [INFO] 原始输入: 
[2025-08-13 03:06:12] [INFO] 解析参数 - 订单号: ORDER17550222267471, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 03:06:12] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550222267471&pid=2222&trade_no=2025081302103684110&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 03:06:12] [INFO] 签名验证 - 期望签名: 5626f0b6fbf71540287a76931fcbee01, 实际签名: 5626f0b6fbf71540287a76931fcbee01
[2025-08-13 03:06:12] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550222267471&payment_status=paid
[2025-08-13 03:06:12] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 03:06:12] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 11:58:14] [INFO] 收到支付通知
[2025-08-13 11:58:14] [INFO] GET参数: {"pid":"2222","trade_no":"2025081311562665448","out_trade_no":"ORDER17550573745826","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"9ecd9e51cd3e092e74fc9ef374b620ed","sign_type":"MD5"}
[2025-08-13 11:58:14] [INFO] POST参数: []
[2025-08-13 11:58:14] [INFO] 原始输入: 
[2025-08-13 11:58:14] [INFO] 解析参数 - 订单号: ORDER17550573745826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 11:58:14] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550573745826&pid=2222&trade_no=2025081311562665448&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 11:58:14] [INFO] 签名验证 - 期望签名: 9ecd9e51cd3e092e74fc9ef374b620ed, 实际签名: 9ecd9e51cd3e092e74fc9ef374b620ed
[2025-08-13 11:58:14] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550573745826&payment_status=paid
[2025-08-13 11:58:14] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 11:58:14] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 11:59:20] [INFO] 收到支付通知
[2025-08-13 11:59:20] [INFO] GET参数: {"pid":"2222","trade_no":"2025081311562665448","out_trade_no":"ORDER17550573745826","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"9ecd9e51cd3e092e74fc9ef374b620ed","sign_type":"MD5"}
[2025-08-13 11:59:20] [INFO] POST参数: []
[2025-08-13 11:59:20] [INFO] 原始输入: 
[2025-08-13 11:59:20] [INFO] 解析参数 - 订单号: ORDER17550573745826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 11:59:20] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550573745826&pid=2222&trade_no=2025081311562665448&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 11:59:20] [INFO] 签名验证 - 期望签名: 9ecd9e51cd3e092e74fc9ef374b620ed, 实际签名: 9ecd9e51cd3e092e74fc9ef374b620ed
[2025-08-13 11:59:20] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550573745826&payment_status=paid
[2025-08-13 11:59:20] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 11:59:20] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 12:01:30] [INFO] 收到支付通知
[2025-08-13 12:01:30] [INFO] GET参数: {"pid":"2222","trade_no":"2025081311562665448","out_trade_no":"ORDER17550573745826","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"9ecd9e51cd3e092e74fc9ef374b620ed","sign_type":"MD5"}
[2025-08-13 12:01:30] [INFO] POST参数: []
[2025-08-13 12:01:30] [INFO] 原始输入: 
[2025-08-13 12:01:30] [INFO] 解析参数 - 订单号: ORDER17550573745826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 12:01:30] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550573745826&pid=2222&trade_no=2025081311562665448&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 12:01:30] [INFO] 签名验证 - 期望签名: 9ecd9e51cd3e092e74fc9ef374b620ed, 实际签名: 9ecd9e51cd3e092e74fc9ef374b620ed
[2025-08-13 12:01:30] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550573745826&payment_status=paid
[2025-08-13 12:01:30] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 12:01:30] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 12:17:32] [INFO] 收到支付通知
[2025-08-13 12:17:32] [INFO] GET参数: {"pid":"2222","trade_no":"2025081311562665448","out_trade_no":"ORDER17550573745826","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"9ecd9e51cd3e092e74fc9ef374b620ed","sign_type":"MD5"}
[2025-08-13 12:17:32] [INFO] POST参数: []
[2025-08-13 12:17:32] [INFO] 原始输入: 
[2025-08-13 12:17:32] [INFO] 解析参数 - 订单号: ORDER17550573745826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 12:17:32] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550573745826&pid=2222&trade_no=2025081311562665448&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 12:17:32] [INFO] 签名验证 - 期望签名: 9ecd9e51cd3e092e74fc9ef374b620ed, 实际签名: 9ecd9e51cd3e092e74fc9ef374b620ed
[2025-08-13 12:17:32] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550573745826&payment_status=paid
[2025-08-13 12:17:32] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 12:17:32] [ERROR] 订单状态更新失败: 订单不存在
[2025-08-13 12:53:36] [INFO] 收到支付通知
[2025-08-13 12:53:36] [INFO] GET参数: {"pid":"2222","trade_no":"2025081311562665448","out_trade_no":"ORDER17550573745826","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"9ecd9e51cd3e092e74fc9ef374b620ed","sign_type":"MD5"}
[2025-08-13 12:53:36] [INFO] POST参数: []
[2025-08-13 12:53:36] [INFO] 原始输入: 
[2025-08-13 12:53:36] [INFO] 解析参数 - 订单号: ORDER17550573745826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-13 12:53:36] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17550573745826&pid=2222&trade_no=2025081311562665448&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-13 12:53:36] [INFO] 签名验证 - 期望签名: 9ecd9e51cd3e092e74fc9ef374b620ed, 实际签名: 9ecd9e51cd3e092e74fc9ef374b620ed
[2025-08-13 12:53:36] [INFO] 调用订单状态更新接口: http://***************:7893/set_order_payment_status.php?order_id=ORDER17550573745826&payment_status=paid
[2025-08-13 12:53:36] [INFO] 订单状态更新结果: {"status":"error","message":"订单不存在","data":null}
[2025-08-13 12:53:36] [ERROR] 订单状态更新失败: 订单不存在
