[2025-08-17 00:19:47] [INFO] 收到支付通知
[2025-08-17 00:19:47] [INFO] GET参数: {"pid":"2222","trade_no":"2025081619530916559","out_trade_no":"ORDER17553451667826","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"fe86731fffe54133c3a5add1f4ecea5a","sign_type":"MD5"}
[2025-08-17 00:19:47] [INFO] POST参数: []
[2025-08-17 00:19:47] [INFO] 原始输入: 
[2025-08-17 00:19:47] [INFO] 解析参数 - 订单号: ORDER17553451667826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-17 00:19:47] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17553451667826&pid=2222&trade_no=2025081619530916559&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-17 00:19:47] [INFO] 签名验证 - 期望签名: fe86731fffe54133c3a5add1f4ecea5a, 实际签名: fe86731fffe54133c3a5add1f4ecea5a
[2025-08-17 00:19:47] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553451667826&payment_status=paid
[2025-08-17 00:19:47] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553451667826","old_status":"paid","new_status":"paid","order_info":{"order_id":"ORDER17553451667826","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-16 19:52:46","delivery_content":"7pVuZrAyX5lWd26","order_status":"paid","customer_contact":"web_1755345166497_36zpt75ew","created_at":"2025-08-16 19:52:46","updated_at":"2025-08-16 19:54:25"}}}
[2025-08-17 00:19:47] [SUCCESS] 订单 ORDER17553451667826 支付状态更新成功
[2025-08-17 07:06:34] [INFO] 收到支付通知
[2025-08-17 07:06:34] [INFO] GET参数: {"pid":"2222","trade_no":"2025081707054755528","out_trade_no":"ORDER17553855287740","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"4db15cba79bc5e7f1ca16c484795be81","sign_type":"MD5"}
[2025-08-17 07:06:34] [INFO] POST参数: []
[2025-08-17 07:06:34] [INFO] 原始输入: 
[2025-08-17 07:06:34] [INFO] 解析参数 - 订单号: ORDER17553855287740, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-17 07:06:34] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17553855287740&pid=2222&trade_no=2025081707054755528&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-17 07:06:34] [INFO] 签名验证 - 期望签名: 4db15cba79bc5e7f1ca16c484795be81, 实际签名: 4db15cba79bc5e7f1ca16c484795be81
[2025-08-17 07:06:34] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553855287740&payment_status=paid
[2025-08-17 07:06:34] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553855287740","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17553855287740","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-17 07:05:28","delivery_content":"7lM0D9ZHKJAgIvO","order_status":"paid","customer_contact":"web_1755385527131_471qrhlk4","created_at":"2025-08-17 07:05:28","updated_at":"2025-08-17 07:06:34"}}}
[2025-08-17 07:06:34] [SUCCESS] 订单 ORDER17553855287740 支付状态更新成功
[2025-08-17 07:10:59] [INFO] 收到支付通知
[2025-08-17 07:10:59] [INFO] GET参数: {"pid":"2222","trade_no":"2025081707105427635","out_trade_no":"ORDER17553858355891","type":"wxpay","name":"product","money":"24.54","trade_status":"TRADE_SUCCESS","sign":"57fdcea74f71d34f26e580899ce01bed","sign_type":"MD5"}
[2025-08-17 07:10:59] [INFO] POST参数: []
[2025-08-17 07:10:59] [INFO] 原始输入: 
[2025-08-17 07:10:59] [INFO] 解析参数 - 订单号: ORDER17553858355891, 支付状态: TRADE_SUCCESS, 金额: 24.54
[2025-08-17 07:10:59] [INFO] 签名验证 - 原始字符串: money=24.54&name=product&out_trade_no=ORDER17553858355891&pid=2222&trade_no=2025081707105427635&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-17 07:10:59] [INFO] 签名验证 - 期望签名: 57fdcea74f71d34f26e580899ce01bed, 实际签名: 57fdcea74f71d34f26e580899ce01bed
[2025-08-17 07:10:59] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553858355891&payment_status=paid
[2025-08-17 07:10:59] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553858355891","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17553858355891","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"24.54","purchase_time":"2025-08-17 07:10:35","delivery_content":"FaC1BoUvV9dpcgN","order_status":"paid","customer_contact":"web_1755385834403_2xqc2oth6","created_at":"2025-08-17 07:10:35","updated_at":"2025-08-17 07:10:59"}}}
[2025-08-17 07:10:59] [SUCCESS] 订单 ORDER17553858355891 支付状态更新成功
[2025-08-17 10:01:18] [INFO] 收到支付通知
[2025-08-17 10:01:18] [INFO] GET参数: {"pid":"2222","trade_no":"2025081710011165995","out_trade_no":"ORDER17553960522565","type":"wxpay","name":"product","money":"26.5","trade_status":"TRADE_SUCCESS","sign":"490f27bcbaaa0cff43be9f434cf9a6ce","sign_type":"MD5"}
[2025-08-17 10:01:18] [INFO] POST参数: []
[2025-08-17 10:01:18] [INFO] 原始输入: 
[2025-08-17 10:01:18] [INFO] 解析参数 - 订单号: ORDER17553960522565, 支付状态: TRADE_SUCCESS, 金额: 26.5
[2025-08-17 10:01:18] [INFO] 签名验证 - 原始字符串: money=26.5&name=product&out_trade_no=ORDER17553960522565&pid=2222&trade_no=2025081710011165995&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-17 10:01:18] [INFO] 签名验证 - 期望签名: 490f27bcbaaa0cff43be9f434cf9a6ce, 实际签名: 490f27bcbaaa0cff43be9f434cf9a6ce
[2025-08-17 10:01:18] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553960522565&payment_status=paid
[2025-08-17 10:01:18] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553960522565","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17553960522565","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"26.50","purchase_time":"2025-08-17 10:00:52","delivery_content":"","order_status":"paid","customer_contact":"web_1755396052071_fxrqlse6a","created_at":"2025-08-17 10:00:52","updated_at":"2025-08-17 10:01:18"}}}
[2025-08-17 10:01:18] [SUCCESS] 订单 ORDER17553960522565 支付状态更新成功
[2025-08-17 12:15:53] [INFO] 收到支付通知
[2025-08-17 12:15:53] [INFO] GET参数: {"pid":"2222","trade_no":"2025081712154422017","out_trade_no":"ORDER17554041241502","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"67e3f053958a374a47e7c462a08d6a27","sign_type":"MD5"}
[2025-08-17 12:15:53] [INFO] POST参数: []
[2025-08-17 12:15:53] [INFO] 原始输入: 
[2025-08-17 12:15:53] [INFO] 解析参数 - 订单号: ORDER17554041241502, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-17 12:15:53] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17554041241502&pid=2222&trade_no=2025081712154422017&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-17 12:15:53] [INFO] 签名验证 - 期望签名: 67e3f053958a374a47e7c462a08d6a27, 实际签名: 67e3f053958a374a47e7c462a08d6a27
[2025-08-17 12:15:53] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554041241502&payment_status=paid
[2025-08-17 12:15:53] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554041241502","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554041241502","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-17 12:15:24","delivery_content":"9tHo1JcauEiV7dv","order_status":"paid","customer_contact":"web_1755404123938_xxxzfx8nd","created_at":"2025-08-17 12:15:24","updated_at":"2025-08-17 12:15:53"}}}
[2025-08-17 12:15:53] [SUCCESS] 订单 ORDER17554041241502 支付状态更新成功
[2025-08-17 17:31:18] [INFO] 收到支付通知
[2025-08-17 17:31:18] [INFO] GET参数: {"pid":"2222","trade_no":"2025081717305270905","out_trade_no":"ORDER17554230303842","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"c3e3d8142feabf3e4e444f3298dad4bd","sign_type":"MD5"}
[2025-08-17 17:31:18] [INFO] POST参数: []
[2025-08-17 17:31:18] [INFO] 原始输入: 
[2025-08-17 17:31:18] [INFO] 解析参数 - 订单号: ORDER17554230303842, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-17 17:31:18] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17554230303842&pid=2222&trade_no=2025081717305270905&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-17 17:31:18] [INFO] 签名验证 - 期望签名: c3e3d8142feabf3e4e444f3298dad4bd, 实际签名: c3e3d8142feabf3e4e444f3298dad4bd
[2025-08-17 17:31:18] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554230303842&payment_status=paid
[2025-08-17 17:31:18] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554230303842","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554230303842","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-17 17:30:30","delivery_content":"zXR1OudTv63H5Eo","order_status":"paid","customer_contact":"web_1755423044759_97ldl1v77","created_at":"2025-08-17 17:30:30","updated_at":"2025-08-17 17:31:18"}}}
[2025-08-17 17:31:18] [SUCCESS] 订单 ORDER17554230303842 支付状态更新成功
[2025-08-17 17:43:37] [INFO] 收到支付通知
[2025-08-17 17:43:37] [INFO] GET参数: {"pid":"2222","trade_no":"2025081717433018363","out_trade_no":"ORDER17554237903447","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"ecb10debaa092b657cd3e2d9bdd3df85","sign_type":"MD5"}
[2025-08-17 17:43:37] [INFO] POST参数: []
[2025-08-17 17:43:37] [INFO] 原始输入: 
[2025-08-17 17:43:37] [INFO] 解析参数 - 订单号: ORDER17554237903447, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-17 17:43:37] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17554237903447&pid=2222&trade_no=2025081717433018363&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-17 17:43:37] [INFO] 签名验证 - 期望签名: ecb10debaa092b657cd3e2d9bdd3df85, 实际签名: ecb10debaa092b657cd3e2d9bdd3df85
[2025-08-17 17:43:37] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554237903447&payment_status=paid
[2025-08-17 17:43:37] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554237903447","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554237903447","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-17 17:43:10","delivery_content":"Fi0La351tr9uVDP","order_status":"paid","customer_contact":"web_1755423789633_ypjeopsh5","created_at":"2025-08-17 17:43:10","updated_at":"2025-08-17 17:43:37"}}}
[2025-08-17 17:43:37] [SUCCESS] 订单 ORDER17554237903447 支付状态更新成功
[2025-08-17 18:24:21] [INFO] 收到支付通知
[2025-08-17 18:24:21] [INFO] GET参数: {"pid":"2222","trade_no":"2025081718234189535","out_trade_no":"ORDER17554261974347","type":"wxpay","name":"product","money":"56.84","trade_status":"TRADE_SUCCESS","sign":"2af8bbbcf942321a338d9b1a4416ac89","sign_type":"MD5"}
[2025-08-17 18:24:21] [INFO] POST参数: []
[2025-08-17 18:24:21] [INFO] 原始输入: 
[2025-08-17 18:24:21] [INFO] 解析参数 - 订单号: ORDER17554261974347, 支付状态: TRADE_SUCCESS, 金额: 56.84
[2025-08-17 18:24:21] [INFO] 签名验证 - 原始字符串: money=56.84&name=product&out_trade_no=ORDER17554261974347&pid=2222&trade_no=2025081718234189535&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-17 18:24:21] [INFO] 签名验证 - 期望签名: 2af8bbbcf942321a338d9b1a4416ac89, 实际签名: 2af8bbbcf942321a338d9b1a4416ac89
[2025-08-17 18:24:21] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554261974347&payment_status=paid
[2025-08-17 18:24:21] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554261974347","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554261974347","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"56.84","purchase_time":"2025-08-17 18:23:17","delivery_content":"trFhbp7axcdviQu","order_status":"paid","customer_contact":"web_1755426197267_ca9e0f2uz","created_at":"2025-08-17 18:23:17","updated_at":"2025-08-17 18:24:21"}}}
[2025-08-17 18:24:21] [SUCCESS] 订单 ORDER17554261974347 支付状态更新成功
[2025-08-17 21:08:46] [INFO] 收到支付通知
[2025-08-17 21:08:46] [INFO] GET参数: {"pid":"2222","trade_no":"2025081721082335267","out_trade_no":"ORDER17554360825086","type":"alipay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"49fdd8249970710d7f27592d80f61295","sign_type":"MD5"}
[2025-08-17 21:08:46] [INFO] POST参数: []
[2025-08-17 21:08:46] [INFO] 原始输入: 
[2025-08-17 21:08:46] [INFO] 解析参数 - 订单号: ORDER17554360825086, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-17 21:08:46] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17554360825086&pid=2222&trade_no=2025081721082335267&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-17 21:08:46] [INFO] 签名验证 - 期望签名: 49fdd8249970710d7f27592d80f61295, 实际签名: 49fdd8249970710d7f27592d80f61295
[2025-08-17 21:08:46] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17554360825086&payment_status=paid
[2025-08-17 21:08:46] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17554360825086","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17554360825086","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-17 21:08:02","delivery_content":"3iLIVRTBmDCPE4q","order_status":"paid","customer_contact":"web_1755436081738_m4yoiisn1","created_at":"2025-08-17 21:08:02","updated_at":"2025-08-17 21:08:46"}}}
[2025-08-17 21:08:46] [SUCCESS] 订单 ORDER17554360825086 支付状态更新成功
