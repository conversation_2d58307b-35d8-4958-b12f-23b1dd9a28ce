[2025-08-16 14:30:27] [INFO] 收到支付通知
[2025-08-16 14:30:27] [INFO] GET参数: {"pid":"2222","trade_no":"2025081614300555293","out_trade_no":"ORDER17553257859959","type":"alipay","name":"product","money":"56.84","trade_status":"TRADE_SUCCESS","sign":"92c17dab7bc6911c4c6e665c1761112e","sign_type":"MD5"}
[2025-08-16 14:30:27] [INFO] POST参数: []
[2025-08-16 14:30:27] [INFO] 原始输入: 
[2025-08-16 14:30:27] [INFO] 解析参数 - 订单号: ORDER17553257859959, 支付状态: TRADE_SUCCESS, 金额: 56.84
[2025-08-16 14:30:27] [INFO] 签名验证 - 原始字符串: money=56.84&name=product&out_trade_no=ORDER17553257859959&pid=2222&trade_no=2025081614300555293&trade_status=TRADE_SUCCESS&type=alipay
[2025-08-16 14:30:27] [INFO] 签名验证 - 期望签名: 92c17dab7bc6911c4c6e665c1761112e, 实际签名: 92c17dab7bc6911c4c6e665c1761112e
[2025-08-16 14:30:27] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553257859959&payment_status=paid
[2025-08-16 14:30:27] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553257859959","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17553257859959","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"56.84","purchase_time":"2025-08-16 14:29:45","delivery_content":"6Dr9W71Pp0oMzOK","order_status":"paid","customer_contact":"6806577465","created_at":"2025-08-16 14:29:45","updated_at":"2025-08-16 14:30:27"}}}
[2025-08-16 14:30:27] [SUCCESS] 订单 ORDER17553257859959 支付状态更新成功
[2025-08-16 19:54:25] [INFO] 收到支付通知
[2025-08-16 19:54:25] [INFO] GET参数: {"pid":"2222","trade_no":"2025081619530916559","out_trade_no":"ORDER17553451667826","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"fe86731fffe54133c3a5add1f4ecea5a","sign_type":"MD5"}
[2025-08-16 19:54:25] [INFO] POST参数: []
[2025-08-16 19:54:25] [INFO] 原始输入: 
[2025-08-16 19:54:25] [INFO] 解析参数 - 订单号: ORDER17553451667826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-16 19:54:25] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17553451667826&pid=2222&trade_no=2025081619530916559&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-16 19:54:25] [INFO] 签名验证 - 期望签名: fe86731fffe54133c3a5add1f4ecea5a, 实际签名: fe86731fffe54133c3a5add1f4ecea5a
[2025-08-16 19:54:25] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553451667826&payment_status=paid
[2025-08-16 19:54:25] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553451667826","old_status":"unpaid","new_status":"paid","order_info":{"order_id":"ORDER17553451667826","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-16 19:52:46","delivery_content":"7pVuZrAyX5lWd26","order_status":"paid","customer_contact":"web_1755345166497_36zpt75ew","created_at":"2025-08-16 19:52:46","updated_at":"2025-08-16 19:54:25"}}}
[2025-08-16 19:54:25] [SUCCESS] 订单 ORDER17553451667826 支付状态更新成功
[2025-08-16 23:46:01] [INFO] 收到支付通知
[2025-08-16 23:46:01] [INFO] GET参数: {"pid":"2222","trade_no":"2025081619530916559","out_trade_no":"ORDER17553451667826","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"fe86731fffe54133c3a5add1f4ecea5a","sign_type":"MD5"}
[2025-08-16 23:46:01] [INFO] POST参数: []
[2025-08-16 23:46:01] [INFO] 原始输入: 
[2025-08-16 23:46:01] [INFO] 解析参数 - 订单号: ORDER17553451667826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-16 23:46:01] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17553451667826&pid=2222&trade_no=2025081619530916559&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-16 23:46:01] [INFO] 签名验证 - 期望签名: fe86731fffe54133c3a5add1f4ecea5a, 实际签名: fe86731fffe54133c3a5add1f4ecea5a
[2025-08-16 23:46:01] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553451667826&payment_status=paid
[2025-08-16 23:46:01] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553451667826","old_status":"paid","new_status":"paid","order_info":{"order_id":"ORDER17553451667826","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-16 19:52:46","delivery_content":"7pVuZrAyX5lWd26","order_status":"paid","customer_contact":"web_1755345166497_36zpt75ew","created_at":"2025-08-16 19:52:46","updated_at":"2025-08-16 19:54:25"}}}
[2025-08-16 23:46:01] [SUCCESS] 订单 ORDER17553451667826 支付状态更新成功
[2025-08-16 23:46:04] [INFO] 收到支付通知
[2025-08-16 23:46:04] [INFO] GET参数: {"pid":"2222","trade_no":"2025081619530916559","out_trade_no":"ORDER17553451667826","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"fe86731fffe54133c3a5add1f4ecea5a","sign_type":"MD5"}
[2025-08-16 23:46:04] [INFO] POST参数: []
[2025-08-16 23:46:04] [INFO] 原始输入: 
[2025-08-16 23:46:04] [INFO] 解析参数 - 订单号: ORDER17553451667826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-16 23:46:04] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17553451667826&pid=2222&trade_no=2025081619530916559&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-16 23:46:04] [INFO] 签名验证 - 期望签名: fe86731fffe54133c3a5add1f4ecea5a, 实际签名: fe86731fffe54133c3a5add1f4ecea5a
[2025-08-16 23:46:04] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553451667826&payment_status=paid
[2025-08-16 23:46:04] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553451667826","old_status":"paid","new_status":"paid","order_info":{"order_id":"ORDER17553451667826","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-16 19:52:46","delivery_content":"7pVuZrAyX5lWd26","order_status":"paid","customer_contact":"web_1755345166497_36zpt75ew","created_at":"2025-08-16 19:52:46","updated_at":"2025-08-16 19:54:25"}}}
[2025-08-16 23:46:04] [SUCCESS] 订单 ORDER17553451667826 支付状态更新成功
[2025-08-16 23:46:06] [INFO] 收到支付通知
[2025-08-16 23:46:06] [INFO] GET参数: {"pid":"2222","trade_no":"2025081619530916559","out_trade_no":"ORDER17553451667826","type":"wxpay","name":"product","money":"9.33","trade_status":"TRADE_SUCCESS","sign":"fe86731fffe54133c3a5add1f4ecea5a","sign_type":"MD5"}
[2025-08-16 23:46:06] [INFO] POST参数: []
[2025-08-16 23:46:06] [INFO] 原始输入: 
[2025-08-16 23:46:06] [INFO] 解析参数 - 订单号: ORDER17553451667826, 支付状态: TRADE_SUCCESS, 金额: 9.33
[2025-08-16 23:46:06] [INFO] 签名验证 - 原始字符串: money=9.33&name=product&out_trade_no=ORDER17553451667826&pid=2222&trade_no=2025081619530916559&trade_status=TRADE_SUCCESS&type=wxpay
[2025-08-16 23:46:06] [INFO] 签名验证 - 期望签名: fe86731fffe54133c3a5add1f4ecea5a, 实际签名: fe86731fffe54133c3a5add1f4ecea5a
[2025-08-16 23:46:06] [INFO] 调用订单状态更新接口: http://**************:10083/set_order_payment_status.php?order_id=ORDER17553451667826&payment_status=paid
[2025-08-16 23:46:06] [INFO] 订单状态更新结果: {"status":"success","message":"订单状态更新成功","data":{"order_id":"ORDER17553451667826","old_status":"paid","new_status":"paid","order_info":{"order_id":"ORDER17553451667826","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"9.33","purchase_time":"2025-08-16 19:52:46","delivery_content":"7pVuZrAyX5lWd26","order_status":"paid","customer_contact":"web_1755345166497_36zpt75ew","created_at":"2025-08-16 19:52:46","updated_at":"2025-08-16 19:54:25"}}}
[2025-08-16 23:46:06] [SUCCESS] 订单 ORDER17553451667826 支付状态更新成功
