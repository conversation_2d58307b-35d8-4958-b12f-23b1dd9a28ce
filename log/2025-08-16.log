[2025-08-16 01:03:03] 查询订单号: ORDER17552773661863
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17552773661863
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-16 01:03:08] 查询订单号: ORDER17552773661863
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17552773661863
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-16 01:05:34] 查询订单号: ORDER17552774456005
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17552774456005
返回: {"code":1,"msg":"succ","trade_no":"2025081601050158042","out_trade_no":"ORDER17552774456005","api_trade_no":"2025081601044559956","bill_trade_no":null,"type":"alipay","pid":"2222","addtime":"2025-08-16 01:05:01","endtime":"2025-08-16 01:05:19","name":"\u7535\u5546\u5355\u53f7:ORDER17552774456005","money":"9.33","param":null,"buyer":null,"status":"1","payurl":null}
[2025-08-16 09:46:23] 查询订单号: ORDER17553087781286
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17553087781286
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-16 09:48:21] 查询订单号: ORDER17553088995637
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17553088995637
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-16 09:58:03] 查询订单号: ORDER17553094335228
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17553094335228
返回: {"code":1,"msg":"succ","trade_no":"2025081609573274948","out_trade_no":"ORDER17553094335228","api_trade_no":"2025081609571775932","bill_trade_no":null,"type":"wxpay","pid":"2222","addtime":"2025-08-16 09:57:32","endtime":"2025-08-16 09:58:06","name":"\u7535\u5546\u5355\u53f7:ORDER17553094335228","money":"9.33","param":null,"buyer":null,"status":"1","payurl":null}
[2025-08-16 10:05:06] 查询订单号: ORDER17553086874581
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17553086874581
返回: {"code":1,"msg":"succ","trade_no":"2025081609450494168","out_trade_no":"ORDER17553086874581","api_trade_no":"2025081609445139057","bill_trade_no":null,"type":"wxpay","pid":"2222","addtime":"2025-08-16 09:45:04","endtime":"2025-08-16 09:45:43","name":"\u7535\u5546\u5355\u53f7:ORDER17553086874581","money":"9.33","param":null,"buyer":null,"status":"1","payurl":null}
[2025-08-16 15:46:49] 查询订单号: ORDER17553304075945
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17553304075945
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
