[2025-08-18 00:31:14] 查询订单号: ORDER17554482695698
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17554482695698
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
[2025-08-18 00:46:14] 查询订单号: ORDER17554491065087
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17554491065087
返回: {"code":1,"msg":"succ","trade_no":"2025081800452981160","out_trade_no":"ORDER17554491065087","api_trade_no":null,"bill_trade_no":null,"type":"wxpay","pid":"2222","addtime":"2025-08-18 00:45:29","endtime":null,"name":"\u7535\u5546\u5355\u53f7:ORDER17554491065087","money":"24.54","param":null,"buyer":null,"status":"0","payurl":null}
[2025-08-18 10:10:49] 查询订单号: ORDER17554830483828
GET URL: https://aftpay.k8s2024.com/api.php?act=order&pid=2222&key=4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA&out_trade_no=ORDER17554830483828
返回: {"code":-1,"msg":"\u8ba2\u5355\u53f7\u4e0d\u5b58\u5728"}
