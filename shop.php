<?php
// 修复 "No input file specified" 错误
if (!isset($_SERVER['REQUEST_URI'])) {
    $_SERVER['REQUEST_URI'] = $_SERVER['PHP_SELF'] . '?' . $_SERVER['QUERY_STRING'];
}

// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 日志函数
function writeLog($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;

    // 写入日志文件
    $logFile = __DIR__ . '/shop_debug.log';
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

    // 同时输出到错误日志
    error_log($logMessage);
}

// 记录访问日志
writeLog("=== 新的访问开始 ===");
writeLog("请求URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A'));
writeLog("请求方法: " . ($_SERVER['REQUEST_METHOD'] ?? 'N/A'));
writeLog("GET参数: " . json_encode($_GET));
writeLog("POST参数: " . json_encode($_POST));
writeLog("User-Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'));
writeLog("客户端IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown'));

// 检查参数
$sj = isset($_GET['sj']) ? $_GET['sj'] : '';
$sp = isset($_GET['sp']) ? $_GET['sp'] : '';
$dd = isset($_GET['dd']) ? $_GET['dd'] : '';

writeLog("解析参数 - sj: '$sj', sp: '$sp', dd: '$dd'");

// 如果没有传递sj、sp或dd参数，返回404
if (empty($sj) && empty($sp) && empty($dd)) {
    writeLog("参数验证失败：sj、sp和dd都为空，返回404", 'ERROR');
    http_response_code(404);
    echo '<!DOCTYPE html><html><head><title>404 Not Found</title></head><body><h1>404 Not Found</h1><p>请提供sj、sp或dd参数</p></body></html>';
    exit;
}

// 确定页面类型
$page_type = 'merchant';
if (!empty($sp)) {
    $page_type = 'product';
} elseif (!empty($dd)) {
    $page_type = 'order';
}

$merchant_id = $sj;
$product_id = $sp;
$order_id = $dd;

writeLog("页面类型: $page_type");
writeLog("商户ID: '$merchant_id'");
writeLog("商品ID: '$product_id'");
writeLog("订单ID: '$order_id'");
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_type === 'merchant' ? '商家店铺' : '商品详情'; ?></title>
    <style>
        /* iOS风格极简设计 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        :root {
            /* 蓝色系专业感 */
            --primary-blue: #007AFF;
            --primary-blue-light: #5AC8FA;
            --primary-blue-dark: #0051D5;
            --blue-gradient: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);

            /* 橙色系活力感 */
            --accent-orange: #FF9500;
            --accent-orange-light: #FFCC02;
            --orange-gradient: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);

            /* iOS系统色彩 */
            --background-primary: #F2F2F7;
            --background-secondary: #FFFFFF;
            --background-tertiary: #F8F9FA;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --text-tertiary: #C7C7CC;
            --separator: #C6C6C8;

            /* 阴影 */
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.15);

            /* 圆角 */
            --radius-small: 8px;
            --radius-medium: 12px;
            --radius-large: 16px;
            --radius-xl: 20px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: var(--background-primary);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 16px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 卡片组件 */
        .card {
            background: var(--background-secondary);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-medium);
            margin-bottom: 24px;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(20px);
        }

        .card-header {
            background: var(--blue-gradient);
            color: white;
            padding: 32px 24px;
            text-align: center;
            position: relative;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .card-header h1,
        .card-header h2 {
            position: relative;
            z-index: 1;
            font-weight: 600;
            font-size: 28px;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .card-body {
            padding: 24px;
        }

        /* 商店信息 */
        .shop-description {
            font-size: 16px;
            line-height: 1.7;
            color: var(--text-secondary);
            white-space: pre-wrap;
            background: var(--background-tertiary);
            padding: 20px;
            border-radius: var(--radius-medium);
            border-left: 4px solid var(--primary-blue);
        }

        /* 商品网格 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .product-item {
            background: var(--background-secondary);
            border-radius: var(--radius-medium);
            padding: 20px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            border: 1px solid var(--separator);
            position: relative;
            overflow: hidden;
        }

        .product-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--orange-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .product-item:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-heavy);
            border-color: var(--primary-blue-light);
        }

        .product-item:hover::before {
            transform: scaleX(1);
        }

        .product-item:active {
            transform: translateY(-2px);
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
            line-height: 1.4;
        }

        .product-price {
            font-size: 24px;
            color: var(--accent-orange);
            font-weight: 700;
            margin-bottom: 12px;
            font-feature-settings: 'tnum';
        }

        .product-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 16px;
            white-space: pre-wrap;
            line-height: 1.5;
        }

        .product-stock {
            font-size: 14px;
            font-weight: 500;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-block;
        }

        .product-stock:not(.out-of-stock) {
            background: rgba(52, 199, 89, 0.1);
            color: #34C759;
        }

        .product-stock.out-of-stock {
            background: rgba(255, 59, 48, 0.1);
            color: #FF3B30;
        }

        /* 按钮系统 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 14px 28px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            margin: 8px;
            min-height: 50px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--blue-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
        }

        .btn-wxpay {
            background: linear-gradient(135deg, #09BB07 0%, #00D100 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(9, 187, 7, 0.3);
        }

        .btn-wxpay:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(9, 187, 7, 0.4);
        }

        .btn-alipay {
            background: linear-gradient(135deg, #1677FF 0%, #40A9FF 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(22, 119, 255, 0.3);
        }

        .btn-alipay:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(22, 119, 255, 0.4);
        }

        .btn:disabled {
            background: var(--text-tertiary);
            color: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn:disabled::before {
            display: none;
        }

        /* 状态组件 */
        .loading, .error {
            text-align: center;
            padding: 60px 20px;
            border-radius: var(--radius-large);
            background: var(--background-secondary);
            margin: 20px 0;
        }

        .loading {
            color: var(--text-secondary);
        }

        .error {
            color: #FF3B30;
            background: rgba(255, 59, 48, 0.05);
            border: 1px solid rgba(255, 59, 48, 0.1);
        }

        /* 订单搜索框 */
        .order-search {
            background: var(--background-secondary);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-medium);
            margin-bottom: 24px;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .order-search-header {
            background: var(--orange-gradient);
            color: white;
            padding: 20px 24px;
            text-align: center;
            position: relative;
        }

        .order-search-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .order-search-header h3 {
            position: relative;
            z-index: 1;
            font-weight: 600;
            font-size: 18px;
            margin: 0;
        }

        .order-search-body {
            padding: 24px;
        }

        .search-form {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-input {
            flex: 1;
            padding: 14px 16px;
            border: 2px solid var(--separator);
            border-radius: var(--radius-medium);
            font-size: 16px;
            transition: all 0.3s ease;
            background: var(--background-tertiary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-orange);
            background: var(--background-secondary);
            box-shadow: 0 0 0 3px rgba(255, 149, 0, 0.1);
        }

        .search-input::placeholder {
            color: var(--text-secondary);
        }

        .search-btn {
            background: var(--orange-gradient);
            color: white;
            border: none;
            padding: 14px 20px;
            border-radius: var(--radius-medium);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 149, 0, 0.3);
        }

        .search-btn:active {
            transform: translateY(0);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .card-header {
                padding: 24px 20px;
            }

            .card-header h1,
            .card-header h2 {
                font-size: 24px;
            }

            .card-body {
                padding: 20px;
            }

            .product-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .product-item {
                padding: 16px;
            }

            .btn {
                width: 100%;
                margin: 6px 0;
            }

            .shop-description {
                padding: 16px;
                font-size: 15px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 12px;
            }

            .card {
                margin-bottom: 16px;
                border-radius: var(--radius-medium);
            }

            .product-name {
                font-size: 16px;
            }

            .product-price {
                font-size: 20px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        .product-item {
            animation: fadeInUp 0.6s ease-out;
        }

        .product-item:nth-child(2) { animation-delay: 0.1s; }
        .product-item:nth-child(3) { animation-delay: 0.2s; }
        .product-item:nth-child(4) { animation-delay: 0.3s; }
        .product-item:nth-child(5) { animation-delay: 0.4s; }
        .product-item:nth-child(6) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="container">
        <div id="app">
            <div class="loading">
                <p>加载中...</p>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'api_proxy.php';
        const pageType = '<?php echo $page_type; ?>';
        const merchantId = '<?php echo htmlspecialchars($merchant_id); ?>';
        const productId = '<?php echo htmlspecialchars($product_id); ?>';
        const orderId = '<?php echo htmlspecialchars($order_id); ?>';
        
        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function formatText(text) {
            if (!text) return '';
            // 处理换行符和emoji
            return escapeHtml(text).replace(/\\n/g, '\n');
        }
        
        // API调用函数
        async function callAPI(action, params = {}) {
            try {
                const url = new URL(API_URL, window.location.origin);
                url.searchParams.append('action', action);

                Object.keys(params).forEach(key => {
                    if (params[key] !== null && params[key] !== undefined) {
                        url.searchParams.append(key, params[key]);
                    }
                });

                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API调用失败:', error);
                return {
                    status: 'error',
                    message: '网络请求失败',
                    data: null
                };
            }
        }

        // 创建订单搜索框
        function createOrderSearchBox() {
            return `
                <div class="order-search">
                    <div class="order-search-header">
                        <h3>🔍 订单查询</h3>
                    </div>
                    <div class="order-search-body">
                        <div class="search-form">
                            <input
                                type="text"
                                id="orderSearchInput"
                                class="search-input"
                                placeholder="请输入电商单号，例如：ORDER17331234567890"
                                onkeypress="handleSearchKeyPress(event)"
                            >
                            <button class="search-btn" onclick="searchOrder()">
                                🔍 查询订单
                            </button>
                        </div>
                        <div style="margin-top: 12px; font-size: 14px; color: var(--text-secondary); text-align: center;">
                            💡 输入完整的电商单号进行查询
                        </div>
                    </div>
                </div>
            `;
        }

        // 处理搜索框回车事件
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                searchOrder();
            }
        }

        // 搜索订单函数
        function searchOrder() {
            const searchInput = document.getElementById('orderSearchInput');
            const orderNumber = searchInput.value.trim();

            if (!orderNumber) {
                alert('请输入订单号');
                searchInput.focus();
                return;
            }

            // 跳转到订单检测页面
            window.open(`shop.php?dd=${encodeURIComponent(orderNumber)}`, '_blank');
        }

        // 渲染商家页面
        async function renderMerchantPage() {
            const app = document.getElementById('app');

            try {
                // 获取商家信息
                const merchantInfo = await callAPI('get_merchant_info', { merchant_id: merchantId });

                if (merchantInfo.status !== 'success') {
                    app.innerHTML = `<div class="error">❌ ${merchantInfo.message}</div>`;
                    return;
                }

                // 获取商品列表
                const productList = await callAPI('get_product_list', { merchant_id: merchantId });

                const merchant = merchantInfo.data;
                const products = productList.status === 'success' ? productList.data : [];

                app.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h1>🏪 ${formatText(merchant.shop_name)}</h1>
                        </div>
                        <div class="card-body">
                            <div class="shop-description">${formatText(merchant.shop_description)}</div>
                        </div>
                    </div>

                    ${createOrderSearchBox()}

                    <div class="card">
                        <div class="card-header">
                            <h2>📦 商品列表</h2>
                        </div>
                        <div class="card-body">
                            <div class="product-grid" id="productGrid">
                                ${products.length === 0 ?
                                    '<div class="error">⚠️ 该商户还没有商品</div>' :
                                    products.map(product => `
                                        <div class="product-item" onclick="showProductDetail('${product.product_id}')">
                                            <div class="product-name">${formatText(product.product_name)}</div>
                                            <div class="product-price">¥${product.product_price}</div>
                                            <div class="product-description">${formatText(product.product_description)}</div>
                                            <div class="product-stock ${parseInt(product.stock_quantity) <= 0 ? 'out-of-stock' : ''}">
                                                ${parseInt(product.stock_quantity) <= 0 ? '❌ 已售罄' : `✅ 库存: ${product.stock_quantity} 件`}
                                            </div>
                                        </div>
                                    `).join('')
                                }
                            </div>
                        </div>
                    </div>
                `;

            } catch (error) {
                app.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
            }
        }

        // 渲染商品详情页面
        async function renderProductPage() {
            const app = document.getElementById('app');

            try {
                const productInfo = await callAPI('get_product_info', { product_id: productId });

                if (productInfo.status !== 'success') {
                    app.innerHTML = `<div class="error">❌ ${productInfo.message}</div>`;
                    return;
                }

                const product = productInfo.data;
                const stockQuantity = parseInt(product.stock_quantity);
                const isOutOfStock = stockQuantity <= 0;

                app.innerHTML = `
                    ${createOrderSearchBox()}

                    <div class="card">
                        <div class="card-header">
                            <h1>📦 商品详情</h1>
                        </div>
                        <div class="card-body">
                            <div class="product-name" style="font-size: 24px; margin-bottom: 15px;">${formatText(product.product_name)}</div>
                            <div class="product-price" style="font-size: 28px; margin-bottom: 15px;">¥${product.product_price}</div>
                            <div class="product-description" style="margin-bottom: 15px;">${formatText(product.product_description)}</div>
                            <div class="product-stock ${isOutOfStock ? 'out-of-stock' : ''}" style="margin-bottom: 20px;">
                                ${isOutOfStock ? '❌ 商品已售罄' : `✅ 库存: ${stockQuantity} 件`}
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                ${isOutOfStock ?
                                    '<button class="btn" disabled>❌ 商品已售罄</button>' :
                                    `
                                    <button class="btn btn-wxpay" onclick="createOrder('wxpay')">💳 微信支付</button>
                                    <button class="btn btn-alipay" onclick="createOrder('alipay')">💳 支付宝</button>
                                    `
                                }
                            </div>
                        </div>
                    </div>
                `;

            } catch (error) {
                app.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
            }
        }

        // 显示商品详情（从商家页面跳转）
        async function showProductDetail(productId) {
            const app = document.getElementById('app');
            app.innerHTML = '<div class="loading"><p>加载中...</p></div>';

            try {
                const productInfo = await callAPI('get_product_info', { product_id: productId });

                if (productInfo.status !== 'success') {
                    app.innerHTML = `<div class="error">❌ ${productInfo.message}</div>`;
                    return;
                }

                const product = productInfo.data;
                const stockQuantity = parseInt(product.stock_quantity);
                const isOutOfStock = stockQuantity <= 0;

                app.innerHTML = `
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-primary" onclick="renderMerchantPage()">🔙 返回商家页面</button>
                    </div>

                    ${createOrderSearchBox()}

                    <div class="card">
                        <div class="card-header">
                            <h1>📦 商品详情</h1>
                        </div>
                        <div class="card-body">
                            <div class="product-name" style="font-size: 24px; margin-bottom: 15px;">${formatText(product.product_name)}</div>
                            <div class="product-price" style="font-size: 28px; margin-bottom: 15px;">¥${product.product_price}</div>
                            <div class="product-description" style="margin-bottom: 15px;">${formatText(product.product_description)}</div>
                            <div class="product-stock ${isOutOfStock ? 'out-of-stock' : ''}" style="margin-bottom: 20px;">
                                ${isOutOfStock ? '❌ 商品已售罄' : `✅ 库存: ${stockQuantity} 件`}
                            </div>

                            <div style="text-align: center; margin-top: 30px;">
                                ${isOutOfStock ?
                                    '<button class="btn" disabled>❌ 商品已售罄</button>' :
                                    `
                                    <button class="btn btn-wxpay" onclick="createOrder('wxpay', '${productId}')">💳 微信支付</button>
                                    <button class="btn btn-alipay" onclick="createOrder('alipay', '${productId}')">💳 支付宝</button>
                                    `
                                }
                            </div>
                        </div>
                    </div>
                `;

            } catch (error) {
                app.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
            }
        }

        // 创建订单
        async function createOrder(payType, pid = null) {
            const currentProductId = pid || productId;

            if (!currentProductId) {
                alert('商品ID不能为空');
                return;
            }

            // 生成客户联系方式（使用时间戳和随机数）
            const customerContact = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const app = document.getElementById('app');
            const originalContent = app.innerHTML;
            app.innerHTML = '<div class="loading"><p>创建订单中...</p></div>';

            try {
                const orderResult = await callAPI('create_order', {
                    customer_contact: customerContact,
                    product_id: currentProductId,
                    pay_type: payType
                });

                if (orderResult.status !== 'success') {
                    alert('❌ ' + orderResult.message);
                    app.innerHTML = originalContent;
                    return;
                }

                const orderInfo = orderResult.data.order_info;
                const paymentUrl = orderResult.data.payment_url;

                app.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h1>🛒 订单创建成功</h1>
                        </div>
                        <div class="card-body">
                            <div style="background: var(--background-tertiary); padding: 24px; border-radius: var(--radius-medium); margin-bottom: 24px; border-left: 4px solid var(--accent-orange);">
                                <h3 style="margin-bottom: 16px; color: var(--text-primary);">订单信息</h3>
                                <div style="margin-bottom: 12px;"><strong>订单号：</strong>${orderInfo.order_id}</div>
                                <div style="margin-bottom: 12px;"><strong>商品名称：</strong>${formatText(orderInfo.product_name)}</div>
                                <div style="margin-bottom: 12px;"><strong>需支付金额：</strong><span style="color: var(--accent-orange); font-weight: 600;">¥${orderInfo.product_price}</span></div>
                                <div style="margin-bottom: 12px;"><strong>购买人ID：</strong>${orderInfo.customer_contact}</div>
                                <div style="margin-bottom: 0;"><strong>订单状态：</strong><span style="color: var(--accent-orange);">待支付</span></div>
                            </div>

                            <div style="text-align: center;">
                                <a href="${paymentUrl}" target="_blank" class="btn btn-primary">💳 立即支付</a>
                                <button class="btn btn-primary" onclick="checkPaymentStatus('${orderInfo.order_id}')">✅ 我已支付</button>
                            </div>
                        </div>
                    </div>
                `;

            } catch (error) {
                alert('❌ 创建订单失败: ' + error.message);
                app.innerHTML = originalContent;
            }
        }

        // 检查支付状态
        async function checkPaymentStatus(orderId) {
            const app = document.getElementById('app');
            const originalContent = app.innerHTML;
            app.innerHTML = '<div class="loading"><p>检查支付状态中...</p></div>';

            try {
                const paymentResult = await callAPI('check_payment_status', { order_id: orderId });

                if (paymentResult.status !== 'success') {
                    alert('❌ 检查支付状态失败');
                    app.innerHTML = originalContent;
                    return;
                }

                const orderData = paymentResult.data;
                const orderStatus = orderData.order_status;

                if (orderStatus === 'paid') {
                    // 支付成功
                    app.innerHTML = `
                        <div class="card">
                            <div class="card-header">
                                <h1>✅ 支付成功</h1>
                            </div>
                            <div class="card-body">
                                <div style="background: var(--background-tertiary); padding: 24px; border-radius: var(--radius-medium); margin-bottom: 24px; border-left: 4px solid #34C759;">
                                    <h3 style="margin-bottom: 16px; color: var(--text-primary);">订单详情</h3>
                                    <div style="margin-bottom: 12px;"><strong>订单号：</strong>${orderData.order_id}</div>
                                    <div style="margin-bottom: 12px;"><strong>商品名称：</strong>${formatText(orderData.product_name)}</div>
                                    <div style="margin-bottom: 12px;"><strong>商品金额：</strong><span style="color: var(--accent-orange); font-weight: 600;">¥${orderData.product_price}</span></div>
                                    <div style="margin-bottom: 12px;"><strong>购买人ID：</strong>${orderData.customer_contact}</div>
                                    <div style="margin-bottom: 0;"><strong>支付时间：</strong>${orderData.updated_at}</div>
                                </div>

                                <div style="background: rgba(52, 199, 89, 0.1); border: 1px solid rgba(52, 199, 89, 0.2); border-radius: var(--radius-medium); padding: 20px; margin: 20px 0; font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;">
                                    <h3 style="margin-bottom: 16px; color: #34C759;">🎁 发货内容：</h3>
                                    <div style="white-space: pre-wrap; word-break: break-all; line-height: 1.6; color: var(--text-primary);">${formatText(orderData.delivery_content)}</div>
                                </div>

                                <div style="text-align: center; margin-top: 30px;">
                                    <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="btn btn-primary">投诉此订单</a>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // 订单未支付
                    alert('❌ 订单未支付！请完成支付后再点击"我已支付"按钮');
                    app.innerHTML = originalContent;
                }

            } catch (error) {
                alert('❌ 检查支付状态失败: ' + error.message);
                app.innerHTML = originalContent;
            }
        }

        // 全局变量用于控制轮询
        let paymentPollingInterval = null;
        let isPollingActive = false;

        // 渲染订单检测页面
        async function renderOrderPage() {
            const app = document.getElementById('app');

            if (!orderId) {
                app.innerHTML = '<div class="error">❌ 订单ID不能为空</div>';
                return;
            }

            app.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h1>🔍 订单查询</h1>
                    </div>
                    <div class="card-body">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <div style="font-size: 18px; color: var(--text-secondary); margin-bottom: 20px;">
                                正在查询订单：<strong>${orderId}</strong>
                            </div>
                            <button class="btn btn-primary" onclick="checkOrderStatus('${orderId}')">🔍 检查订单状态</button>
                        </div>
                    </div>
                </div>
            `;

            // 自动检查订单状态
            setTimeout(() => {
                checkOrderStatus(orderId);
            }, 500);
        }

        // 停止轮询
        function stopPaymentPolling() {
            if (paymentPollingInterval) {
                clearInterval(paymentPollingInterval);
                paymentPollingInterval = null;
                isPollingActive = false;
                console.log('支付状态轮询已停止');
            }
        }

        // 开始轮询支付状态
        function startPaymentPolling(orderIdToCheck) {
            // 先停止之前的轮询
            stopPaymentPolling();

            isPollingActive = true;
            console.log('开始轮询支付状态，每5秒检查一次');

            paymentPollingInterval = setInterval(async () => {
                if (!isPollingActive) {
                    stopPaymentPolling();
                    return;
                }

                try {
                    const paymentResult = await callAPI('check_payment_status', { order_id: orderIdToCheck });
                    if (paymentResult.status === 'success' && paymentResult.data.order_status === 'paid') {
                        console.log('检测到支付成功，停止轮询');
                        stopPaymentPolling();
                        checkOrderStatus(orderIdToCheck); // 重新渲染页面显示支付成功状态
                    }
                } catch (error) {
                    console.error('轮询检查支付状态失败:', error);
                }
            }, 5000); // 每5秒检查一次
        }

        // 检查订单状态（与checkPaymentStatus相同的逻辑）
        async function checkOrderStatus(orderIdToCheck) {
            const app = document.getElementById('app');
            const originalContent = app.innerHTML;

            app.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h1>🔍 订单查询</h1>
                    </div>
                    <div class="card-body">
                        <div class="loading">
                            <p>正在查询订单状态...</p>
                        </div>
                    </div>
                </div>
            `;

            try {
                const paymentResult = await callAPI('check_payment_status', { order_id: orderIdToCheck });

                if (paymentResult.status !== 'success') {
                    app.innerHTML = `
                        <div class="card">
                            <div class="card-header">
                                <h1>❌ 查询失败</h1>
                            </div>
                            <div class="card-body">
                                <div class="error">
                                    <p>${paymentResult.message || '查询订单状态失败'}</p>
                                    <button class="btn btn-primary" onclick="checkOrderStatus('${orderIdToCheck}')">重新查询</button>
                                </div>
                            </div>
                        </div>
                    `;
                    return;
                }

                const orderData = paymentResult.data;
                const orderStatus = orderData.order_status;

                if (orderStatus === 'paid') {
                    // 支付成功，停止轮询并显示完整订单信息
                    stopPaymentPolling();
                    app.innerHTML = `
                        <div class="card">
                            <div class="card-header">
                                <h1>✅ 支付成功</h1>
                            </div>
                            <div class="card-body">
                                <div style="background: var(--background-tertiary); padding: 24px; border-radius: var(--radius-medium); margin-bottom: 24px; border-left: 4px solid #34C759;">
                                    <h3 style="margin-bottom: 16px; color: var(--text-primary);">订单详情</h3>
                                    <div style="margin-bottom: 12px;"><strong>订单号：</strong>${orderData.order_id}</div>
                                    <div style="margin-bottom: 12px;"><strong>商品名称：</strong>${formatText(orderData.product_name)}</div>
                                    <div style="margin-bottom: 12px;"><strong>商品金额：</strong><span style="color: var(--accent-orange); font-weight: 600;">¥${orderData.product_price}</span></div>
                                    <div style="margin-bottom: 12px;"><strong>购买人ID：</strong>${orderData.customer_contact}</div>
                                    <div style="margin-bottom: 0;"><strong>支付时间：</strong>${orderData.updated_at}</div>
                                </div>

                                <div style="background: rgba(52, 199, 89, 0.1); border: 1px solid rgba(52, 199, 89, 0.2); border-radius: var(--radius-medium); padding: 20px; margin: 20px 0; font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;">
                                    <h3 style="margin-bottom: 16px; color: #34C759;">🎁 发货内容：</h3>
                                    <div style="white-space: pre-wrap; word-break: break-all; line-height: 1.6; color: var(--text-primary);">${formatText(orderData.delivery_content)}</div>
                                </div>

                                <div style="text-align: center; margin-top: 30px;">
                                    <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="btn btn-primary">投诉此订单</a>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // 订单未支付，显示轮询状态
                    const pollingStatusText = isPollingActive ?
                        '<span style="color: #34C759;">🔄 自动检查中（每5秒）</span>' :
                        '<span style="color: var(--text-secondary);">⏸️ 已暂停自动检查</span>';

                    app.innerHTML = `
                        <div class="card">
                            <div class="card-header">
                                <h1>⏳ 订单未支付</h1>
                            </div>
                            <div class="card-body">
                                <div style="background: var(--background-tertiary); padding: 24px; border-radius: var(--radius-medium); margin-bottom: 24px; border-left: 4px solid var(--accent-orange);">
                                    <h3 style="margin-bottom: 16px; color: var(--text-primary);">订单信息</h3>
                                    <div style="margin-bottom: 12px;"><strong>订单号：</strong>${orderData.order_id}</div>
                                    <div style="margin-bottom: 12px;"><strong>商品名称：</strong>${formatText(orderData.product_name)}</div>
                                    <div style="margin-bottom: 12px;"><strong>商品金额：</strong><span style="color: var(--accent-orange); font-weight: 600;">¥${orderData.product_price}</span></div>
                                    <div style="margin-bottom: 12px;"><strong>购买人ID：</strong>${orderData.customer_contact}</div>
                                    <div style="margin-bottom: 12px;"><strong>订单状态：</strong><span style="color: var(--accent-orange);">待支付</span></div>
                                    <div style="margin-bottom: 0;"><strong>检查状态：</strong>${pollingStatusText}</div>
                                </div>

                                <div style="text-align: center; margin-bottom: 20px;">
                                    <button class="btn btn-primary" onclick="checkOrderStatus('${orderIdToCheck}')" style="margin-right: 10px;">🔄 立即检查</button>
                                    ${isPollingActive ?
                                        '<button class="btn btn-secondary" onclick="stopPaymentPolling()">⏸️ 停止自动检查</button>' :
                                        '<button class="btn btn-primary" onclick="startPaymentPolling(\'' + orderIdToCheck + '\')">▶️ 开始自动检查</button>'
                                    }
                                </div>

                                <div style="background: rgba(255, 149, 0, 0.1); border: 1px solid rgba(255, 149, 0, 0.2); border-radius: var(--radius-medium); padding: 16px; margin-top: 20px; text-align: center;">
                                    <p style="color: var(--accent-orange); margin: 0;">💡 完成支付后，系统将自动检测并更新订单状态</p>
                                </div>
                            </div>
                        </div>
                    `;

                    // 如果订单未支付且没有在轮询，自动开始轮询
                    if (!isPollingActive) {
                        startPaymentPolling(orderIdToCheck);
                    }
                }

            } catch (error) {
                app.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h1>❌ 查询失败</h1>
                        </div>
                        <div class="card-body">
                            <div class="error">
                                <p>查询订单状态失败: ${error.message}</p>
                                <button class="btn btn-primary" onclick="checkOrderStatus('${orderIdToCheck}')">重新查询</button>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (pageType === 'merchant') {
                renderMerchantPage();
            } else if (pageType === 'product') {
                renderProductPage();
            } else if (pageType === 'order') {
                renderOrderPage();
            }
        });

        // 页面卸载时停止轮询
        window.addEventListener('beforeunload', function() {
            stopPaymentPolling();
        });

        // 页面隐藏时停止轮询，页面显示时恢复轮询
        document.addEventListener('visibilitychange', function() {
            if (pageType === 'order' && orderId) {
                if (document.hidden) {
                    stopPaymentPolling();
                } else {
                    // 页面重新显示时，如果订单未支付则重新开始轮询
                    setTimeout(() => {
                        checkOrderStatus(orderId);
                    }, 1000);
                }
            }
        });
    </script>
    
    <script> var _mtj = _mtj || []; (function () { var mtj = document.createElement("script"); mtj.src = "https://node94.aizhantj.com:21233/tjjs/?k=4ym1ptfqdvv"; var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(mtj, s); })(); </script>
</body>
</html>
